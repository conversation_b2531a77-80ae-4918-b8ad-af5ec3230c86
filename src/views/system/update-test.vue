<script setup lang="ts">
import { onMounted, ref } from 'vue'
import type { UpdateInfo } from '../../../electron/types/update-service'
import { UpdateStatus } from '../../../electron/types/update-service'

defineOptions({
  name: 'UpdateTest',
})

const updateInfo = ref<UpdateInfo>({ status: UpdateStatus.NOT_AVAILABLE })
const currentVersion = ref('未知版本')
const isLoading = ref(false)

// 更新状态监听器
const handleUpdateStatus = (info: UpdateInfo) => {
  updateInfo.value = info
  console.log('更新状态变化:', info)
}

// 检查更新
const handleCheckUpdate = async () => {
  if (!window.electronAPI?.updateService) {
    window.$message?.error('Electron API 不可用')
    return
  }
  
  isLoading.value = true
  try {
    const result = await window.electronAPI.updateService.checkForUpdates()
    if (result.success) {
      window.$message?.success('检查更新请求已发送')
    } else {
      window.$message?.error(`检查更新失败: ${result.error}`)
    }
  } catch (error) {
    console.error('检查更新失败:', error)
    window.$message?.error('检查更新失败')
  } finally {
    isLoading.value = false
  }
}

// 下载更新
const handleDownloadUpdate = async () => {
  if (!window.electronAPI?.updateService) {
    window.$message?.error('Electron API 不可用')
    return
  }
  
  isLoading.value = true
  try {
    const result = await window.electronAPI.updateService.downloadUpdate()
    if (result.success) {
      window.$message?.success('下载更新请求已发送')
    } else {
      window.$message?.error(`下载更新失败: ${result.error}`)
    }
  } catch (error) {
    console.error('下载更新失败:', error)
    window.$message?.error('下载更新失败')
  } finally {
    isLoading.value = false
  }
}

// 安装更新
const handleInstallUpdate = async () => {
  if (!window.electronAPI?.updateService) {
    window.$message?.error('Electron API 不可用')
    return
  }
  
  try {
    const result = await window.electronAPI.updateService.installUpdate()
    if (result.success) {
      window.$message?.success('安装更新请求已发送，应用即将重启')
    } else {
      window.$message?.error(`安装更新失败: ${result.error}`)
    }
  } catch (error) {
    console.error('安装更新失败:', error)
    window.$message?.error('安装更新失败')
  }
}

// 获取更新信息
const handleGetUpdateInfo = async () => {
  if (!window.electronAPI?.updateService) {
    window.$message?.error('Electron API 不可用')
    return
  }
  
  try {
    const info = await window.electronAPI.updateService.getUpdateInfo()
    updateInfo.value = info
    window.$message?.success('更新信息已刷新')
  } catch (error) {
    console.error('获取更新信息失败:', error)
    window.$message?.error('获取更新信息失败')
  }
}

// 生命周期
onMounted(async () => {
  if (window.electronAPI?.updateService) {
    // 初始化更新服务
    window.electronAPI.updateService.initialize()
    
    // 添加监听器
    window.electronAPI.updateService.addUpdateListener(handleUpdateStatus)
    
    // 获取当前版本和更新信息
    try {
      const [version, info] = await Promise.all([
        window.electronAPI.updateService.getCurrentVersion(),
        window.electronAPI.updateService.getUpdateInfo()
      ])
      currentVersion.value = version
      updateInfo.value = info
    } catch (error) {
      console.error('初始化失败:', error)
    }
  } else {
    window.$message?.warning('当前不在Electron环境中，更新功能不可用')
  }
})

onUnmounted(() => {
  if (window.electronAPI?.updateService) {
    window.electronAPI.updateService.removeUpdateListener(handleUpdateStatus)
  }
})
</script>

<template>
  <div class="p-6 space-y-6">
    <NCard title="Electron 更新测试">
      <div class="space-y-4">
        <!-- 版本信息 -->
        <div class="grid grid-cols-2 gap-4">
          <NCard size="small" title="当前版本">
            <div class="text-lg font-mono">{{ currentVersion }}</div>
          </NCard>
          
          <NCard size="small" title="更新状态">
            <div class="flex items-center gap-2">
              <NTag
                :type="updateInfo.status === UpdateStatus.ERROR ? 'error' : 
                      updateInfo.status === UpdateStatus.AVAILABLE || updateInfo.status === UpdateStatus.DOWNLOADED ? 'success' : 
                      'default'"
              >
                {{ updateInfo.status }}
              </NTag>
              <span v-if="updateInfo.version" class="text-sm text-gray-500">
                v{{ updateInfo.version }}
              </span>
            </div>
          </NCard>
        </div>
        
        <!-- 下载进度 -->
        <div v-if="updateInfo.status === UpdateStatus.DOWNLOADING && updateInfo.progress">
          <NCard size="small" title="下载进度">
            <div class="space-y-2">
              <NProgress
                type="line"
                :percentage="updateInfo.progress.percent"
                :show-indicator="true"
                processing
              />
              <div class="flex justify-between text-sm text-gray-500">
                <span>{{ updateInfo.progress.percent.toFixed(1) }}%</span>
                <span>{{ (updateInfo.progress.bytesPerSecond / 1024 / 1024).toFixed(1) }} MB/s</span>
              </div>
              <div class="text-xs text-gray-400">
                {{ (updateInfo.progress.transferred / 1024 / 1024).toFixed(1) }} MB / 
                {{ (updateInfo.progress.total / 1024 / 1024).toFixed(1) }} MB
              </div>
            </div>
          </NCard>
        </div>
        
        <!-- 发布说明 -->
        <div v-if="updateInfo.releaseNotes">
          <NCard size="small" title="发布说明">
            <div class="text-sm whitespace-pre-wrap max-h-32 overflow-y-auto">
              {{ updateInfo.releaseNotes }}
            </div>
          </NCard>
        </div>
        
        <!-- 错误信息 -->
        <div v-if="updateInfo.error">
          <NCard size="small" title="错误信息">
            <div class="text-sm text-red-500">
              {{ updateInfo.error }}
            </div>
          </NCard>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex gap-3 flex-wrap">
          <NButton
            type="primary"
            :loading="isLoading"
            @click="handleCheckUpdate"
          >
            检查更新
          </NButton>
          
          <NButton
            :disabled="updateInfo.status !== UpdateStatus.AVAILABLE"
            :loading="isLoading"
            @click="handleDownloadUpdate"
          >
            下载更新
          </NButton>
          
          <NButton
            :disabled="updateInfo.status !== UpdateStatus.DOWNLOADED"
            type="warning"
            @click="handleInstallUpdate"
          >
            安装更新
          </NButton>
          
          <NButton
            secondary
            @click="handleGetUpdateInfo"
          >
            刷新状态
          </NButton>
        </div>
        
        <!-- 调试信息 -->
        <NCard size="small" title="调试信息">
          <pre class="text-xs bg-gray-50 p-2 rounded overflow-auto max-h-40">{{ JSON.stringify(updateInfo, null, 2) }}</pre>
        </NCard>
      </div>
    </NCard>
  </div>
</template>

<style scoped></style>
