export {}

declare global {
  export interface Window {
    /** NProgress 实例 */
    NProgress?: import('nprogress').NProgress
    /** 加载条实例 */
    $loadingBar?: import('naive-ui').LoadingBarProviderInst
    /** 对话框实例 */
    $dialog?: import('naive-ui').DialogProviderInst
    /** 消息实例 */
    $message?: import('naive-ui').MessageProviderInst
    /** 通知实例 */
    $notification?: import('naive-ui').NotificationProviderInst
    /** Electron API */
    electronAPI?: {
      updateService: {
        initialize(): void
        checkForUpdates(): Promise<import('../../electron/main/services/base-service').ServiceResult<void>>
        downloadUpdate(): Promise<import('../../electron/main/services/base-service').ServiceResult<void>>
        installUpdate(): Promise<import('../../electron/main/services/base-service').ServiceResult<void>>
        getUpdateInfo(): Promise<import('../../electron/types/update-service').UpdateInfo>
        getCurrentVersion(): Promise<string>
        addUpdateListener(listener: (info: import('../../electron/types/update-service').UpdateInfo) => void): void
        removeUpdateListener(listener: (info: import('../../electron/types/update-service').UpdateInfo) => void): void
      }
    }
  }

  /** 项目的构建时间 */
  export const BUILD_TIME: string
}
