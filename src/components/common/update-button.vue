<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import type { UpdateInfo } from '../../../electron/types/update-service'
import { UpdateStatus } from '../../../electron/types/update-service'

defineOptions({
  name: 'UpdateButton',
})

// 更新信息状态
const updateInfo = ref<UpdateInfo>({ status: UpdateStatus.NOT_AVAILABLE })
const currentVersion = ref('未知版本')
const showDropdown = ref(false)

// 计算属性
const hasUpdate = computed(() => {
  return [UpdateStatus.AVAILABLE, UpdateStatus.DOWNLOADED].includes(updateInfo.value.status)
})

const isChecking = computed(() => {
  return updateInfo.value.status === UpdateStatus.CHECKING
})

const isDownloading = computed(() => {
  return updateInfo.value.status === UpdateStatus.DOWNLOADING
})

const buttonIcon = computed(() => {
  if (isChecking.value) return 'material-symbols:sync'
  if (hasUpdate.value) return 'material-symbols:system-update-alt'
  return 'material-symbols:info-outline'
})

const buttonType = computed(() => {
  if (hasUpdate.value) return 'primary'
  if (updateInfo.value.status === UpdateStatus.ERROR) return 'error'
  return 'default'
})

const statusText = computed(() => {
  switch (updateInfo.value.status) {
    case UpdateStatus.CHECKING:
      return '检查更新中...'
    case UpdateStatus.AVAILABLE:
      return `有新版本 ${updateInfo.value.version || ''}`
    case UpdateStatus.DOWNLOADING:
      return `下载中 ${(updateInfo.value.progress?.percent || 0).toFixed(0)}%`
    case UpdateStatus.DOWNLOADED:
      return '更新已下载'
    case UpdateStatus.INSTALLING:
      return '安装中...'
    case UpdateStatus.ERROR:
      return '更新失败'
    case UpdateStatus.NOT_AVAILABLE:
      return '已是最新版本'
    default:
      return '未知状态'
  }
})

// 更新状态监听器
const handleUpdateStatus = (info: UpdateInfo) => {
  updateInfo.value = info
}

// 操作方法
const handleCheckUpdate = async () => {
  if (!window.electronAPI?.updateService) return
  
  try {
    await window.electronAPI.updateService.checkForUpdates()
    window.$message?.info('正在检查更新...')
  } catch (error) {
    console.error('检查更新失败:', error)
    window.$message?.error('检查更新失败')
  }
}

const handleDownload = async () => {
  if (!window.electronAPI?.updateService) return
  
  try {
    await window.electronAPI.updateService.downloadUpdate()
    window.$message?.info('开始下载更新...')
  } catch (error) {
    console.error('下载更新失败:', error)
    window.$message?.error('下载更新失败')
  }
}

const handleInstall = async () => {
  if (!window.electronAPI?.updateService) return
  
  try {
    await window.electronAPI.updateService.installUpdate()
  } catch (error) {
    console.error('安装更新失败:', error)
    window.$message?.error('安装更新失败')
  }
}

// 生命周期
onMounted(async () => {
  // 初始化更新服务
  if (window.electronAPI?.updateService) {
    window.electronAPI.updateService.initialize()
    window.electronAPI.updateService.addUpdateListener(handleUpdateStatus)
    
    // 获取当前版本和更新状态
    try {
      const [version, info] = await Promise.all([
        window.electronAPI.updateService.getCurrentVersion(),
        window.electronAPI.updateService.getUpdateInfo()
      ])
      currentVersion.value = version
      handleUpdateStatus(info)
    } catch (error) {
      console.error('获取版本信息失败:', error)
    }
  }
})

onUnmounted(() => {
  // 移除监听器
  if (window.electronAPI?.updateService) {
    window.electronAPI.updateService.removeUpdateListener(handleUpdateStatus)
  }
})
</script>

<template>
  <NDropdown
    v-model:show="showDropdown"
    trigger="click"
    placement="bottom-end"
  >
    <ButtonIcon
      :icon="buttonIcon"
      :tooltip-content="statusText"
      :class="{
        'animate-spin': isChecking,
        'text-primary': hasUpdate,
        'text-error': updateInfo.status === UpdateStatus.ERROR
      }"
    />
    
    <template #default>
      <div class="w-64 p-4 space-y-3">
        <!-- 版本信息 -->
        <div class="text-sm">
          <div class="font-medium mb-1">当前版本</div>
          <div class="text-gray-600">{{ currentVersion }}</div>
        </div>
        
        <!-- 更新状态 -->
        <div class="text-sm">
          <div class="font-medium mb-1">更新状态</div>
          <div class="text-gray-600">{{ statusText }}</div>
        </div>
        
        <!-- 下载进度 -->
        <div v-if="isDownloading && updateInfo.progress" class="text-sm">
          <div class="font-medium mb-2">下载进度</div>
          <NProgress
            type="line"
            :percentage="updateInfo.progress.percent"
            :show-indicator="true"
            processing
          />
          <div class="flex justify-between text-xs text-gray-500 mt-1">
            <span>{{ updateInfo.progress.percent.toFixed(1) }}%</span>
            <span>
              {{ (updateInfo.progress.bytesPerSecond / 1024 / 1024).toFixed(1) }} MB/s
            </span>
          </div>
        </div>
        
        <!-- 新版本信息 -->
        <div v-if="updateInfo.version && hasUpdate" class="text-sm">
          <div class="font-medium mb-1">新版本</div>
          <div class="text-gray-600">{{ updateInfo.version }}</div>
        </div>
        
        <!-- 发布说明 -->
        <div v-if="updateInfo.releaseNotes && hasUpdate" class="text-sm">
          <div class="font-medium mb-1">更新内容</div>
          <div class="text-gray-600 text-xs max-h-20 overflow-y-auto whitespace-pre-wrap">
            {{ updateInfo.releaseNotes }}
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex gap-2 pt-2 border-t">
          <NButton
            v-if="updateInfo.status === UpdateStatus.NOT_AVAILABLE || updateInfo.status === UpdateStatus.ERROR"
            size="small"
            type="primary"
            :loading="isChecking"
            @click="handleCheckUpdate"
          >
            检查更新
          </NButton>
          
          <NButton
            v-if="updateInfo.status === UpdateStatus.AVAILABLE"
            size="small"
            type="primary"
            @click="handleDownload"
          >
            下载更新
          </NButton>
          
          <NButton
            v-if="updateInfo.status === UpdateStatus.DOWNLOADED"
            size="small"
            type="primary"
            @click="handleInstall"
          >
            立即安装
          </NButton>
        </div>
      </div>
    </template>
  </NDropdown>
</template>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
