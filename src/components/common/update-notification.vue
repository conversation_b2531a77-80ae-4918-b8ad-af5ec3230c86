<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import type { UpdateInfo } from '../../../electron/types/update-service'
import { UpdateStatus } from '../../../electron/types/update-service'

defineOptions({
  name: 'UpdateNotification',
})

// 更新信息状态
const updateInfo = ref<UpdateInfo>({ status: UpdateStatus.NOT_AVAILABLE })
const showNotification = ref(false)
const isDownloading = ref(false)

// 计算属性
const statusText = computed(() => {
  switch (updateInfo.value.status) {
    case UpdateStatus.CHECKING:
      return '正在检查更新...'
    case UpdateStatus.AVAILABLE:
      return `发现新版本 ${updateInfo.value.version || ''}`
    case UpdateStatus.DOWNLOADING:
      return '正在下载更新...'
    case UpdateStatus.DOWNLOADED:
      return '更新下载完成'
    case UpdateStatus.INSTALLING:
      return '正在安装更新...'
    case UpdateStatus.ERROR:
      return `更新失败: ${updateInfo.value.error || '未知错误'}`
    case UpdateStatus.NOT_AVAILABLE:
      return '当前已是最新版本'
    default:
      return '未知状态'
  }
})

const progressPercent = computed(() => {
  return updateInfo.value.progress?.percent || 0
})

const downloadSpeed = computed(() => {
  const speed = updateInfo.value.progress?.bytesPerSecond || 0
  if (speed < 1024) return `${speed.toFixed(0)} B/s`
  if (speed < 1024 * 1024) return `${(speed / 1024).toFixed(1)} KB/s`
  return `${(speed / (1024 * 1024)).toFixed(1)} MB/s`
})

const showProgress = computed(() => {
  return updateInfo.value.status === UpdateStatus.DOWNLOADING && updateInfo.value.progress
})

const showActions = computed(() => {
  return [UpdateStatus.AVAILABLE, UpdateStatus.DOWNLOADED, UpdateStatus.ERROR].includes(updateInfo.value.status)
})

// 更新状态监听器
const handleUpdateStatus = (info: UpdateInfo) => {
  updateInfo.value = info
  
  // 显示通知的条件
  if ([
    UpdateStatus.AVAILABLE,
    UpdateStatus.DOWNLOADED,
    UpdateStatus.ERROR
  ].includes(info.status)) {
    showNotification.value = true
  }
  
  // 自动隐藏某些状态的通知
  if ([
    UpdateStatus.NOT_AVAILABLE,
    UpdateStatus.INSTALLING
  ].includes(info.status)) {
    showNotification.value = false
  }
}

// 操作方法
const handleDownload = async () => {
  if (!window.electronAPI?.updateService) return
  
  isDownloading.value = true
  try {
    await window.electronAPI.updateService.downloadUpdate()
  } catch (error) {
    console.error('下载更新失败:', error)
    window.$message?.error('下载更新失败')
  } finally {
    isDownloading.value = false
  }
}

const handleInstall = async () => {
  if (!window.electronAPI?.updateService) return
  
  try {
    await window.electronAPI.updateService.installUpdate()
  } catch (error) {
    console.error('安装更新失败:', error)
    window.$message?.error('安装更新失败')
  }
}

const handleCheckUpdate = async () => {
  if (!window.electronAPI?.updateService) return
  
  try {
    await window.electronAPI.updateService.checkForUpdates()
    window.$message?.info('正在检查更新...')
  } catch (error) {
    console.error('检查更新失败:', error)
    window.$message?.error('检查更新失败')
  }
}

const handleClose = () => {
  showNotification.value = false
}

// 生命周期
onMounted(() => {
  // 初始化更新服务并添加监听器
  if (window.electronAPI?.updateService) {
    window.electronAPI.updateService.initialize()
    window.electronAPI.updateService.addUpdateListener(handleUpdateStatus)
    
    // 获取当前更新状态
    window.electronAPI.updateService.getUpdateInfo().then((info: UpdateInfo) => {
      handleUpdateStatus(info)
    })
  }
})

onUnmounted(() => {
  // 移除监听器
  if (window.electronAPI?.updateService) {
    window.electronAPI.updateService.removeUpdateListener(handleUpdateStatus)
  }
})
</script>

<template>
  <div v-if="showNotification" class="fixed top-4 right-4 z-50">
    <NCard class="w-80 shadow-lg">
      <template #header>
        <div class="flex items-center justify-between">
          <span class="font-medium">应用更新</span>
          <NButton quaternary circle size="small" @click="handleClose">
            <template #icon>
              <SvgIcon icon="material-symbols:close" />
            </template>
          </NButton>
        </div>
      </template>
      
      <div class="space-y-3">
        <!-- 状态文本 -->
        <div class="text-sm text-gray-600">
          {{ statusText }}
        </div>
        
        <!-- 下载进度 -->
        <div v-if="showProgress" class="space-y-2">
          <NProgress
            type="line"
            :percentage="progressPercent"
            :show-indicator="true"
            processing
          />
          <div class="flex justify-between text-xs text-gray-500">
            <span>{{ progressPercent.toFixed(1) }}%</span>
            <span>{{ downloadSpeed }}</span>
          </div>
        </div>
        
        <!-- 发布说明 -->
        <div v-if="updateInfo.releaseNotes && updateInfo.status === UpdateStatus.AVAILABLE" class="text-xs text-gray-500 max-h-20 overflow-y-auto">
          <div class="font-medium mb-1">更新内容:</div>
          <div class="whitespace-pre-wrap">{{ updateInfo.releaseNotes }}</div>
        </div>
        
        <!-- 操作按钮 -->
        <div v-if="showActions" class="flex gap-2 justify-end">
          <NButton
            v-if="updateInfo.status === UpdateStatus.AVAILABLE"
            size="small"
            type="primary"
            :loading="isDownloading"
            @click="handleDownload"
          >
            下载更新
          </NButton>
          
          <NButton
            v-if="updateInfo.status === UpdateStatus.DOWNLOADED"
            size="small"
            type="primary"
            @click="handleInstall"
          >
            立即安装
          </NButton>
          
          <NButton
            v-if="updateInfo.status === UpdateStatus.ERROR"
            size="small"
            type="primary"
            @click="handleCheckUpdate"
          >
            重新检查
          </NButton>
          
          <NButton size="small" @click="handleClose">
            稍后处理
          </NButton>
        </div>
      </div>
    </NCard>
  </div>
</template>

<style scoped>
.update-notification-enter-active,
.update-notification-leave-active {
  transition: all 0.3s ease;
}

.update-notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.update-notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}
</style>
